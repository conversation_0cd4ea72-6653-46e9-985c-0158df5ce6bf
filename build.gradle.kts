plugins {
    id("java")
    id("com.github.johnrengelman.shadow") version "8.1.1"
}

group = "com.tfc"
version = "1.0-SNAPSHOT"
repositories {

    mavenCentral()
}

dependencies {
    compileOnly("org.jetbrains:annotations:13.0")
    implementation("org.apache.pdfbox:pdfbox:3.0.5")
    implementation("org.slf4j:slf4j-api:2.0.13")
    implementation("ch.qos.logback:logback-classic:1.5.6")
    implementation("info.picocli:picocli:4.7.5")
    testImplementation(platform("org.junit:junit-bom:5.10.0"))
    testImplementation("org.junit.jupiter:junit-jupiter")
}

tasks.test {
    useJUnitPlatform()
    testLogging {
        events("passed", "skipped", "failed", "standardOut", "standardError")
        showStandardStreams = true
    }
}

// Add manifest for executable jar
tasks.jar {
    manifest {
        attributes("Main-Class" to "com.tfc.Main")
    }
}

tasks.shadowJar {
    manifest {
        attributes("Main-Class" to "com.tfc.Main")
    }
}
// To build a fat jar: ./gradlew shadowJar
// The output will be in build/libs/pdf-engine-1.0-SNAPSHOT-all.jar
