package com.tfc.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SizeTest {

    @Test
    void testToString() {
        Size size = new Size();
        size.width = 100.5f;
        size.height = 200.7f;
        
        String expected = "Size{width=100.5, height=200.7}";
        assertEquals(expected, size.toString());
    }

    @Test
    void testToStringWithZeroValues() {
        Size size = new Size();
        size.width = 0.0f;
        size.height = 0.0f;
        
        String expected = "Size{width=0.0, height=0.0}";
        assertEquals(expected, size.toString());
    }

    @Test
    void testToStringWithNegativeValues() {
        Size size = new Size();
        size.width = -50.3f;
        size.height = -75.8f;
        
        String expected = "Size{width=-50.3, height=-75.8}";
        assertEquals(expected, size.toString());
    }

    @Test
    void testFieldsArePublic() {
        Size size = new Size();
        
        // Test that fields can be accessed and modified directly
        size.width = 300.0f;
        size.height = 400.0f;
        
        assertEquals(300.0f, size.width);
        assertEquals(400.0f, size.height);
    }
}
