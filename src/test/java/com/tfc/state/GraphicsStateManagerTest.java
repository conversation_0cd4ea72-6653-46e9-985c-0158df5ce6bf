package com.tfc.state;

import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.cos.COSFloat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class GraphicsStateManagerTest {

    private GraphicsStateManager graphicsStateManager;

    @BeforeEach
    void setUp() {
        graphicsStateManager = new GraphicsStateManager();
    }

    @Test
    void testInitialState() {
        double[] ctm = graphicsStateManager.getCTM();
        
        // Initial CTM should be identity matrix
        assertArrayEquals(new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0}, ctm, 0.001);
    }

    @Test
    void testSaveGraphicsState() {
        // Modify CTM first
        List<COSBase> cmOperands = Arrays.asList(
                new COSFloat(2.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(2.0f),
                new COSFloat(10.0f), new COSFloat(20.0f)
        );
        graphicsStateManager.addCM(cmOperands);

        // Save the state
        graphicsStateManager.saveGraphicsState();

        // Modify CTM again
        List<COSBase> cmOperands2 = Arrays.asList(
                new COSFloat(3.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(3.0f),
                new COSFloat(5.0f), new COSFloat(15.0f)
        );
        graphicsStateManager.addCM(cmOperands2);

        // CTM should be different now
        double[] currentCTM = graphicsStateManager.getCTM();
        assertNotEquals(2.0, currentCTM[0], 0.001);
    }

    @Test
    void testRestoreGraphicsState() {
        // Modify CTM
        List<COSBase> cmOperands = Arrays.asList(
                new COSFloat(2.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(2.0f),
                new COSFloat(10.0f), new COSFloat(20.0f)
        );
        graphicsStateManager.addCM(cmOperands);

        // Save the state
        graphicsStateManager.saveGraphicsState();

        // Modify CTM again
        List<COSBase> cmOperands2 = Arrays.asList(
                new COSFloat(3.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(3.0f),
                new COSFloat(5.0f), new COSFloat(15.0f)
        );
        graphicsStateManager.addCM(cmOperands2);

        // Restore the state
        graphicsStateManager.restoreGraphicsState();

        // CTM should be back to the saved state
        double[] restoredCTM = graphicsStateManager.getCTM();
        assertEquals(2.0, restoredCTM[0], 0.001);
        assertEquals(0.0, restoredCTM[1], 0.001);
        assertEquals(0.0, restoredCTM[2], 0.001);
        assertEquals(2.0, restoredCTM[3], 0.001);
        assertEquals(10.0, restoredCTM[4], 0.001);
        assertEquals(20.0, restoredCTM[5], 0.001);
    }

    @Test
    void testRestoreGraphicsStateWithEmptyStack() {
        // Modify CTM
        List<COSBase> cmOperands = Arrays.asList(
                new COSFloat(2.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(2.0f),
                new COSFloat(10.0f), new COSFloat(20.0f)
        );
        graphicsStateManager.addCM(cmOperands);

        // Restore without saving (empty stack)
        graphicsStateManager.restoreGraphicsState();

        // CTM should be reset to identity
        double[] ctm = graphicsStateManager.getCTM();
        assertArrayEquals(new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0}, ctm, 0.001);
    }

    @Test
    void testNestedSaveRestore() {
        // Initial state
        double[] initialCTM = graphicsStateManager.getCTM();

        // First modification and save
        List<COSBase> cmOperands1 = Arrays.asList(
                new COSFloat(2.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(2.0f),
                new COSFloat(0.0f), new COSFloat(0.0f)
        );
        graphicsStateManager.addCM(cmOperands1);
        graphicsStateManager.saveGraphicsState();

        // Second modification and save
        List<COSBase> cmOperands2 = Arrays.asList(
                new COSFloat(1.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(1.0f),
                new COSFloat(10.0f), new COSFloat(20.0f)
        );
        graphicsStateManager.addCM(cmOperands2);
        graphicsStateManager.saveGraphicsState();

        // Third modification
        List<COSBase> cmOperands3 = Arrays.asList(
                new COSFloat(3.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(3.0f),
                new COSFloat(5.0f), new COSFloat(15.0f)
        );
        graphicsStateManager.addCM(cmOperands3);

        // First restore - should go back to second state
        graphicsStateManager.restoreGraphicsState();
        double[] secondStateCTM = graphicsStateManager.getCTM();
        assertEquals(2.0, secondStateCTM[0], 0.001);
        assertEquals(20.0, secondStateCTM[4], 0.001); // 2*10 = 20
        assertEquals(40.0, secondStateCTM[5], 0.001); // 2*20 = 40

        // Second restore - should go back to first state
        graphicsStateManager.restoreGraphicsState();
        double[] firstStateCTM = graphicsStateManager.getCTM();
        assertEquals(2.0, firstStateCTM[0], 0.001);
        assertEquals(0.0, firstStateCTM[4], 0.001);
        assertEquals(0.0, firstStateCTM[5], 0.001);
    }

    @Test
    void testAddCM() {
        List<COSBase> cmOperands = Arrays.asList(
                new COSFloat(2.0f), new COSFloat(0.5f),
                new COSFloat(-0.5f), new COSFloat(1.5f),
                new COSFloat(10.0f), new COSFloat(20.0f)
        );

        graphicsStateManager.addCM(cmOperands);

        double[] ctm = graphicsStateManager.getCTM();
        assertEquals(2.0, ctm[0], 0.001);
        assertEquals(0.5, ctm[1], 0.001);
        assertEquals(-0.5, ctm[2], 0.001);
        assertEquals(1.5, ctm[3], 0.001);
        assertEquals(10.0, ctm[4], 0.001);
        assertEquals(20.0, ctm[5], 0.001);
    }

    @Test
    void testMultipleAddCM() {
        // First transformation: scale by 2
        List<COSBase> cmOperands1 = Arrays.asList(
                new COSFloat(2.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(2.0f),
                new COSFloat(0.0f), new COSFloat(0.0f)
        );
        graphicsStateManager.addCM(cmOperands1);

        // Second transformation: translate by (10, 20)
        List<COSBase> cmOperands2 = Arrays.asList(
                new COSFloat(1.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(1.0f),
                new COSFloat(10.0f), new COSFloat(20.0f)
        );
        graphicsStateManager.addCM(cmOperands2);

        double[] ctm = graphicsStateManager.getCTM();
        assertEquals(2.0, ctm[0], 0.001);  // Scale preserved
        assertEquals(0.0, ctm[1], 0.001);
        assertEquals(0.0, ctm[2], 0.001);
        assertEquals(2.0, ctm[3], 0.001);  // Scale preserved
        assertEquals(20.0, ctm[4], 0.001); // Translation gets scaled: 2*10 = 20
        assertEquals(40.0, ctm[5], 0.001); // Translation gets scaled: 2*20 = 40
    }

    @Test
    void testGetCTM() {
        double[] initialCTM = graphicsStateManager.getCTM();
        
        // Should return identity matrix initially
        assertArrayEquals(new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0}, initialCTM, 0.001);
        
        // Modify CTM
        List<COSBase> cmOperands = Arrays.asList(
                new COSFloat(3.0f), new COSFloat(1.0f),
                new COSFloat(-1.0f), new COSFloat(2.0f),
                new COSFloat(15.0f), new COSFloat(25.0f)
        );
        graphicsStateManager.addCM(cmOperands);

        double[] modifiedCTM = graphicsStateManager.getCTM();
        assertEquals(3.0, modifiedCTM[0], 0.001);
        assertEquals(1.0, modifiedCTM[1], 0.001);
        assertEquals(-1.0, modifiedCTM[2], 0.001);
        assertEquals(2.0, modifiedCTM[3], 0.001);
        assertEquals(15.0, modifiedCTM[4], 0.001);
        assertEquals(25.0, modifiedCTM[5], 0.001);
    }

    @Test
    void testTransformPoint() {
        // Set up a transformation matrix
        List<COSBase> cmOperands = Arrays.asList(
                new COSFloat(2.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(3.0f),
                new COSFloat(10.0f), new COSFloat(20.0f)
        );
        graphicsStateManager.addCM(cmOperands);

        double[] result = graphicsStateManager.transformPoint(5.0, 7.0);

        // Expected: x' = 2*5 + 0*7 + 10 = 20, y' = 0*5 + 3*7 + 20 = 41
        assertEquals(20.0, result[0], 0.001);
        assertEquals(41.0, result[1], 0.001);
    }

    @Test
    void testTransformPointWithIdentityMatrix() {
        double[] result = graphicsStateManager.transformPoint(10.0, 15.0);

        // With identity matrix, point should remain unchanged
        assertEquals(10.0, result[0], 0.001);
        assertEquals(15.0, result[1], 0.001);
    }

    @Test
    void testSaveRestorePreservesTransformations() {
        // Apply initial transformation
        List<COSBase> cmOperands1 = Arrays.asList(
                new COSFloat(2.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(2.0f),
                new COSFloat(5.0f), new COSFloat(10.0f)
        );
        graphicsStateManager.addCM(cmOperands1);

        // Save state
        graphicsStateManager.saveGraphicsState();

        // Apply another transformation
        List<COSBase> cmOperands2 = Arrays.asList(
                new COSFloat(1.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(1.0f),
                new COSFloat(3.0f), new COSFloat(7.0f)
        );
        graphicsStateManager.addCM(cmOperands2);

        // Test point transformation in current state
        double[] currentResult = graphicsStateManager.transformPoint(1.0, 1.0);
        assertEquals(13.0, currentResult[0], 0.001); // 2*1 + (2*3 + 5) = 2 + 11 = 13
        assertEquals(26.0, currentResult[1], 0.001); // 2*1 + (2*7 + 10) = 2 + 24 = 26

        // Restore state
        graphicsStateManager.restoreGraphicsState();

        // Test point transformation in restored state
        double[] restoredResult = graphicsStateManager.transformPoint(1.0, 1.0);
        assertEquals(7.0, restoredResult[0], 0.001); // 2*1 + 5
        assertEquals(12.0, restoredResult[1], 0.001); // 2*1 + 10
    }
}
