package com.tfc.model;

import org.junit.jupiter.api.Test;
import java.awt.image.BufferedImage;

import static org.junit.jupiter.api.Assertions.*;

class ImageTest {

    @Test
    void testInheritsFromElement() {
        Image image = new Image();
        
        // Test that Image is an instance of Element
        assertTrue(image instanceof Element);
    }

    @Test
    void testToStringWithNullValues() {
        Image image = new Image();

        String result = image.toString();
        assertTrue(result.contains("Image{"));
        assertTrue(result.contains("id="));
        assertTrue(result.contains("size=null"));
        assertTrue(result.contains("position=null"));
        assertTrue(result.contains(image.getId()));
    }

    @Test
    void testToStringWithSize() {
        Image image = new Image();
        Size size = new Size();
        size.width = 100.0f;
        size.height = 200.0f;
        image.size = size;
        
        String expected = "Image{size=Size{width=100.0, height=200.0}, position=null}";
        assertEquals(expected, image.toString());
    }

    @Test
    void testToStringWithPosition() {
        Image image = new Image();
        Position position = new Position();
        position.x = 50.0f;
        position.y = 75.0f;
        image.position = position;
        
        String expected = "Image{size=null, position=Position{x=50.0, y=75.0}}";
        assertEquals(expected, image.toString());
    }

    @Test
    void testToStringWithSizeAndPosition() {
        Image image = new Image();
        
        Size size = new Size();
        size.width = 300.0f;
        size.height = 400.0f;
        image.size = size;
        
        Position position = new Position();
        position.x = 10.0f;
        position.y = 20.0f;
        image.position = position;
        
        String expected = "Image{size=Size{width=300.0, height=400.0}, position=Position{x=10.0, y=20.0}}";
        assertEquals(expected, image.toString());
    }

    @Test
    void testFieldsArePublic() {
        Image image = new Image();
        
        Size size = new Size();
        size.width = 150.0f;
        size.height = 250.0f;
        
        BufferedImage bufferedImage = new BufferedImage(100, 100, BufferedImage.TYPE_INT_RGB);
        
        // Test that fields can be accessed and modified directly
        image.size = size;
        image.bufferedImage = bufferedImage;
        
        assertSame(size, image.size);
        assertSame(bufferedImage, image.bufferedImage);
        assertEquals(150.0f, image.size.width);
        assertEquals(250.0f, image.size.height);
    }

    @Test
    void testBufferedImageField() {
        Image image = new Image();
        BufferedImage bufferedImage = new BufferedImage(50, 75, BufferedImage.TYPE_INT_ARGB);
        
        image.bufferedImage = bufferedImage;
        
        assertSame(bufferedImage, image.bufferedImage);
        assertEquals(50, image.bufferedImage.getWidth());
        assertEquals(75, image.bufferedImage.getHeight());
        assertEquals(BufferedImage.TYPE_INT_ARGB, image.bufferedImage.getType());
    }
}
