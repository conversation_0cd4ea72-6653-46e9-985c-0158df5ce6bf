package com.tfc.model;

import org.junit.jupiter.api.Test;
import java.awt.Color;

import static org.junit.jupiter.api.Assertions.*;

class RGBATest {

    @Test
    void testConstructorWithParameters() {
        RGBA rgba = new RGBA(255, 128, 64, 32);
        
        assertEquals(255, rgba.r);
        assertEquals(128, rgba.g);
        assertEquals(64, rgba.b);
        assertEquals(32, rgba.a);
    }

    @Test
    void testDefaultConstructor() {
        RGBA rgba = new RGBA();
        
        assertEquals(0, rgba.r);
        assertEquals(0, rgba.g);
        assertEquals(0, rgba.b);
        assertEquals(0, rgba.a);
    }

    @Test
    void testToString() {
        RGBA rgba = new RGBA(255, 128, 64, 32);
        String expected = "RGBA{r=255, g=128, b=64, a=32, hex=#FF804020}";
        
        assertEquals(expected, rgba.toString());
    }

    @Test
    void testToStringWithZeroValues() {
        RGBA rgba = new RGBA(0, 0, 0, 0);
        String expected = "RGBA{r=0, g=0, b=0, a=0, hex=#00000000}";
        
        assertEquals(expected, rgba.toString());
    }

    @Test
    void testToColor() {
        RGBA rgba = new RGBA(255, 128, 64, 32);
        Color color = rgba.toColor();

        assertEquals(255, color.getRed());
        assertEquals(128, color.getGreen());
        assertEquals(64, color.getBlue());
        assertEquals(32, color.getAlpha());
    }

    @Test
    void testToColorWithZeroValues() {
        RGBA rgba = new RGBA(0, 0, 0, 0);
        Color color = rgba.toColor();

        assertEquals(0, color.getRed());
        assertEquals(0, color.getGreen());
        assertEquals(0, color.getBlue());
        assertEquals(0, color.getAlpha());
    }

    @Test
    void testIsWhiteTrue() {
        RGBA rgba = new RGBA(255, 255, 255, 255);
        
        assertTrue(rgba.isWhite());
    }

    @Test
    void testIsWhiteFalseWithDifferentRed() {
        RGBA rgba = new RGBA(254, 255, 255, 255);
        
        assertFalse(rgba.isWhite());
    }

    @Test
    void testIsWhiteFalseWithDifferentGreen() {
        RGBA rgba = new RGBA(255, 254, 255, 255);
        
        assertFalse(rgba.isWhite());
    }

    @Test
    void testIsWhiteFalseWithDifferentBlue() {
        RGBA rgba = new RGBA(255, 255, 254, 255);
        
        assertFalse(rgba.isWhite());
    }

    @Test
    void testIsWhiteFalseWithDifferentAlpha() {
        RGBA rgba = new RGBA(255, 255, 255, 254);
        
        assertFalse(rgba.isWhite());
    }

    @Test
    void testIsWhiteFalseWithAllZeros() {
        RGBA rgba = new RGBA(0, 0, 0, 0);
        
        assertFalse(rgba.isWhite());
    }
}
