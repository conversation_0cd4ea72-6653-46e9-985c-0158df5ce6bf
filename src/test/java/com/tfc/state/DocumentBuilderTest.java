package com.tfc.state;

import com.tfc.model.*;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.awt.geom.Point2D;
import java.awt.image.BufferedImage;

import static org.junit.jupiter.api.Assertions.*;

class DocumentBuilderTest {

    private DocumentBuilder documentBuilder;

    @BeforeEach
    void setUp() {
        documentBuilder = new DocumentBuilder();
    }

    @Test
    void testInitialState() {
        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        
        assertNotNull(pageStructure);
        assertTrue(pageStructure.getElements().isEmpty());
        assertTrue(pageStructure.getShapes().isEmpty());
        assertTrue(pageStructure.getLines().isEmpty());
        assertTrue(pageStructure.getImages().isEmpty());
    }

    @Test
    void testAddTextElement() {
        PDFTextElement textElement = new PDFTextElement(
                "Test Text",
                new Point2D.Float(10.0f, 20.0f),
                12.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        documentBuilder.addTextElement(textElement);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(1, pageStructure.getElements().size());
        assertSame(textElement, pageStructure.getElements().get(0));
    }

    @Test
    void testAddMultipleTextElements() {
        PDFTextElement element1 = new PDFTextElement(
                "Text 1",
                new Point2D.Float(0.0f, 0.0f),
                10.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        PDFTextElement element2 = new PDFTextElement(
                "Text 2",
                new Point2D.Float(10.0f, 10.0f),
                14.0f,
                new PDType1Font(Standard14Fonts.FontName.TIMES_ROMAN),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        documentBuilder.addTextElement(element1);
        documentBuilder.addTextElement(element2);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(2, pageStructure.getElements().size());
        assertSame(element1, pageStructure.getElements().get(0));
        assertSame(element2, pageStructure.getElements().get(1));
    }

    @Test
    void testAddShapeElement() {
        RGBA fillColor = new RGBA(255, 0, 0, 255);
        RGBA strokeColor = new RGBA(0, 255, 0, 255);
        PDFShapeElement shapeElement = new PDFShapeElement(10.0f, 20.0f, 30.0f, 40.0f, fillColor, strokeColor);

        documentBuilder.addShapeElement(shapeElement);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(1, pageStructure.getShapes().size());
        assertSame(shapeElement, pageStructure.getShapes().get(0));
    }

    @Test
    void testAddMultipleShapeElements() {
        PDFShapeElement shape1 = new PDFShapeElement(0.0f, 0.0f, 10.0f, 10.0f, null, null);
        PDFShapeElement shape2 = new PDFShapeElement(20.0f, 20.0f, 30.0f, 30.0f, null, null);

        documentBuilder.addShapeElement(shape1);
        documentBuilder.addShapeElement(shape2);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(2, pageStructure.getShapes().size());
        assertSame(shape1, pageStructure.getShapes().get(0));
        assertSame(shape2, pageStructure.getShapes().get(1));
    }

    @Test
    void testAddLineElement() {
        RGBA strokeColor = new RGBA(0, 0, 255, 255);
        PDFLineElement lineElement = new PDFLineElement(0.0f, 0.0f, 100.0f, 100.0f, strokeColor);

        documentBuilder.addLineElement(lineElement);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(1, pageStructure.getLines().size());
        assertSame(lineElement, pageStructure.getLines().get(0));
    }

    @Test
    void testAddMultipleLineElements() {
        PDFLineElement line1 = new PDFLineElement(0.0f, 0.0f, 10.0f, 10.0f, null);
        PDFLineElement line2 = new PDFLineElement(20.0f, 20.0f, 30.0f, 30.0f, null);

        documentBuilder.addLineElement(line1);
        documentBuilder.addLineElement(line2);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(2, pageStructure.getLines().size());
        assertSame(line1, pageStructure.getLines().get(0));
        assertSame(line2, pageStructure.getLines().get(1));
    }

    @Test
    void testAddImageElement() {
        Image image = new Image();
        image.size = new Size();
        image.size.width = 100.0f;
        image.size.height = 200.0f;
        image.position = new Position();
        image.position.x = 50.0f;
        image.position.y = 75.0f;
        image.bufferedImage = new BufferedImage(100, 200, BufferedImage.TYPE_INT_RGB);

        documentBuilder.addImageElement(image);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(1, pageStructure.getImages().size());
        assertSame(image, pageStructure.getImages().get(0));
    }

    @Test
    void testAddMultipleImageElements() {
        Image image1 = new Image();
        image1.size = new Size();
        image1.size.width = 50.0f;
        image1.size.height = 50.0f;

        Image image2 = new Image();
        image2.size = new Size();
        image2.size.width = 100.0f;
        image2.size.height = 100.0f;

        documentBuilder.addImageElement(image1);
        documentBuilder.addImageElement(image2);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(2, pageStructure.getImages().size());
        assertSame(image1, pageStructure.getImages().get(0));
        assertSame(image2, pageStructure.getImages().get(1));
    }

    @Test
    void testAddMixedElements() {
        // Add one of each type of element
        PDFTextElement textElement = new PDFTextElement(
                "Mixed Test",
                new Point2D.Float(5.0f, 10.0f),
                11.0f,
                new PDType1Font(Standard14Fonts.FontName.COURIER),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        RGBA fillColor = new RGBA(128, 128, 128, 255);
        PDFShapeElement shapeElement = new PDFShapeElement(15.0f, 25.0f, 35.0f, 45.0f, fillColor, null);

        RGBA strokeColor = new RGBA(64, 64, 64, 255);
        PDFLineElement lineElement = new PDFLineElement(5.0f, 5.0f, 55.0f, 55.0f, strokeColor);

        Image image = new Image();
        image.size = new Size();
        image.size.width = 75.0f;
        image.size.height = 125.0f;

        documentBuilder.addTextElement(textElement);
        documentBuilder.addShapeElement(shapeElement);
        documentBuilder.addLineElement(lineElement);
        documentBuilder.addImageElement(image);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(1, pageStructure.getElements().size());
        assertEquals(1, pageStructure.getShapes().size());
        assertEquals(1, pageStructure.getLines().size());
        assertEquals(1, pageStructure.getImages().size());

        assertSame(textElement, pageStructure.getElements().get(0));
        assertSame(shapeElement, pageStructure.getShapes().get(0));
        assertSame(lineElement, pageStructure.getLines().get(0));
        assertSame(image, pageStructure.getImages().get(0));
    }

    @Test
    void testGetPDFPageStructureReturnsSameInstance() {
        PDFPageStructure pageStructure1 = documentBuilder.getPDFPageStructure();
        PDFPageStructure pageStructure2 = documentBuilder.getPDFPageStructure();

        assertSame(pageStructure1, pageStructure2);
    }

    @Test
    void testElementsAreAddedInOrder() {
        PDFTextElement text1 = new PDFTextElement(
                "First",
                new Point2D.Float(0.0f, 0.0f),
                10.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        PDFTextElement text2 = new PDFTextElement(
                "Second",
                new Point2D.Float(10.0f, 10.0f),
                10.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        PDFTextElement text3 = new PDFTextElement(
                "Third",
                new Point2D.Float(20.0f, 20.0f),
                10.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        documentBuilder.addTextElement(text1);
        documentBuilder.addTextElement(text2);
        documentBuilder.addTextElement(text3);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals("First", pageStructure.getElements().get(0).getText());
        assertEquals("Second", pageStructure.getElements().get(1).getText());
        assertEquals("Third", pageStructure.getElements().get(2).getText());
    }

    @Test
    void testNullElementHandling() {
        // Test that null elements can be added (though this might not be desired behavior)
        documentBuilder.addTextElement(null);
        documentBuilder.addShapeElement(null);
        documentBuilder.addLineElement(null);
        documentBuilder.addImageElement(null);

        PDFPageStructure pageStructure = documentBuilder.getPDFPageStructure();
        assertEquals(1, pageStructure.getElements().size());
        assertEquals(1, pageStructure.getShapes().size());
        assertEquals(1, pageStructure.getLines().size());
        assertEquals(1, pageStructure.getImages().size());

        assertNull(pageStructure.getElements().get(0));
        assertNull(pageStructure.getShapes().get(0));
        assertNull(pageStructure.getLines().get(0));
        assertNull(pageStructure.getImages().get(0));
    }
}
