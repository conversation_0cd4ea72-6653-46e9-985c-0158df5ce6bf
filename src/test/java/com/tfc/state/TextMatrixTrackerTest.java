package com.tfc.state;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.awt.geom.AffineTransform;
import java.awt.geom.Point2D;

import static org.junit.jupiter.api.Assertions.*;

class TextMatrixTrackerTest {

    private TextMatrixTracker textMatrixTracker;

    @BeforeEach
    void setUp() {
        textMatrixTracker = new TextMatrixTracker();
    }

    @Test
    void testInitialState() {
        // Initial text matrix should be identity
        assertTrue(textMatrixTracker.getTextMatrix().isIdentity());
    }

    @Test
    void testSetTextMatrixWithIdentity() {
        AffineTransform identityMatrix = new AffineTransform();

        textMatrixTracker.setTextMatrix(identityMatrix);

        assertTrue(textMatrixTracker.getTextMatrix().isIdentity());
    }

    @Test
    void testSetTextMatrixWithTranslation() {
        AffineTransform translationMatrix = AffineTransform.getTranslateInstance(10.0, 20.0);

        textMatrixTracker.setTextMatrix(translationMatrix);

        assertFalse(textMatrixTracker.getTextMatrix().isIdentity());

        // Test transformation of origin point
        Point2D origin = new Point2D.Float(0.0f, 0.0f);
        Point2D transformed = textMatrixTracker.getTextMatrix().transform(origin, null);

        assertEquals(10.0, transformed.getX(), 0.001);
        assertEquals(20.0, transformed.getY(), 0.001);
    }

    @Test
    void testSetTextMatrixWithScale() {
        AffineTransform scaleMatrix = AffineTransform.getScaleInstance(2.0, 3.0);

        textMatrixTracker.setTextMatrix(scaleMatrix);

        // Test transformation of a point
        Point2D testPoint = new Point2D.Float(5.0f, 7.0f);
        Point2D transformed = textMatrixTracker.getTextMatrix().transform(testPoint, null);

        assertEquals(10.0, transformed.getX(), 0.001); // 5 * 2
        assertEquals(21.0, transformed.getY(), 0.001); // 7 * 3
    }

    @Test
    void testSetTextMatrixWithComplexTransformation() {
        AffineTransform complexMatrix = new AffineTransform(2.0, 0.5, -0.5, 1.5, 10.0, 20.0);

        textMatrixTracker.setTextMatrix(complexMatrix);

        Point2D testPoint = new Point2D.Float(1.0f, 1.0f);
        Point2D transformed = textMatrixTracker.getTextMatrix().transform(testPoint, null);

        // Manual calculation: x' = 2*1 + (-0.5)*1 + 10 = 11.5, y' = 0.5*1 + 1.5*1 + 20 = 22.0
        assertEquals(11.5, transformed.getX(), 0.001);
        assertEquals(22.0, transformed.getY(), 0.001);
    }

    @Test
    void testTranslate() {
        float tx = 15.0f;
        float ty = 25.0f;

        textMatrixTracker.translate(tx, ty);

        // After translation, transforming origin should give the translation values
        Point2D origin = new Point2D.Float(0.0f, 0.0f);
        Point2D transformed = textMatrixTracker.getTextMatrix().transform(origin, null);

        assertEquals(tx, transformed.getX(), 0.001);
        assertEquals(ty, transformed.getY(), 0.001);
    }

    @Test
    void testMultipleTranslations() {
        textMatrixTracker.translate(10.0f, 20.0f);
        textMatrixTracker.translate(5.0f, 7.0f);

        // Translations should accumulate
        Point2D origin = new Point2D.Float(0.0f, 0.0f);
        Point2D transformed = textMatrixTracker.getTextMatrix().transform(origin, null);

        assertEquals(15.0, transformed.getX(), 0.001); // 10 + 5
        assertEquals(27.0, transformed.getY(), 0.001); // 20 + 7
    }

    @Test
    void testTranslationAfterSetMatrix() {
        // Set initial matrix with translation
        AffineTransform initialMatrix = AffineTransform.getTranslateInstance(100.0, 200.0);
        textMatrixTracker.setTextMatrix(initialMatrix);

        // Apply additional translation
        textMatrixTracker.translate(10.0f, 20.0f);

        Point2D origin = new Point2D.Float(0.0f, 0.0f);
        Point2D transformed = textMatrixTracker.getTextMatrix().transform(origin, null);

        assertEquals(110.0, transformed.getX(), 0.001); // 100 + 10
        assertEquals(220.0, transformed.getY(), 0.001); // 200 + 20
    }

    @Test
    void testGetTextPositionWithNullOrigin() {
        AffineTransform matrix = AffineTransform.getTranslateInstance(50.0, 75.0);
        textMatrixTracker.setTextMatrix(matrix);

        Point2D result = textMatrixTracker.getTextPosition(null);

        // Should use (0,0) as default origin
        assertEquals(50.0, result.getX(), 0.001);
        assertEquals(75.0, result.getY(), 0.001);
    }

    @Test
    void testGetTextPositionWithOrigin() {
        AffineTransform matrix = new AffineTransform(2.0, 0.0, 0.0, 2.0, 10.0, 20.0);
        textMatrixTracker.setTextMatrix(matrix);

        Point2D origin = new Point2D.Float(5.0f, 7.0f);
        Point2D result = textMatrixTracker.getTextPosition(origin);

        // Expected: x' = 2*5 + 10 = 20, y' = 2*7 + 20 = 34
        assertEquals(20.0, result.getX(), 0.001);
        assertEquals(34.0, result.getY(), 0.001);
    }

    @Test
    void testCopy() {
        // Set up a custom text matrix
        AffineTransform customMatrix = new AffineTransform(2.0, 0.5, -0.5, 1.5, 10.0, 20.0);
        textMatrixTracker.setTextMatrix(customMatrix);

        TextMatrixTracker copy = textMatrixTracker.copy();

        // Copy should have the same transformation behavior
        Point2D testPoint = new Point2D.Float(1.0f, 1.0f);
        Point2D originalResult = textMatrixTracker.getTextMatrix().transform(testPoint, null);
        Point2D copyResult = copy.getTextMatrix().transform(testPoint, null);

        assertEquals(originalResult.getX(), copyResult.getX(), 0.001);
        assertEquals(originalResult.getY(), copyResult.getY(), 0.001);

        // But should be a different object
        assertNotSame(textMatrixTracker, copy);
        assertNotSame(textMatrixTracker.getTextMatrix(), copy.getTextMatrix());

        // Modifying the copy should not affect the original
        copy.translate(100.0f, 100.0f);

        Point2D originalAfterCopyModification = textMatrixTracker.getTextMatrix().transform(testPoint, null);
        assertEquals(originalResult.getX(), originalAfterCopyModification.getX(), 0.001);
        assertEquals(originalResult.getY(), originalAfterCopyModification.getY(), 0.001);
    }

    @Test
    void testCopyWithIdentityMatrix() {
        TextMatrixTracker copy = textMatrixTracker.copy();

        // Copy should also have identity matrix
        assertTrue(copy.getTextMatrix().isIdentity());
        assertNotSame(textMatrixTracker, copy);
    }

    @Test
    void testZeroTranslation() {
        textMatrixTracker.translate(0.0f, 0.0f);

        // Should still be identity matrix
        assertTrue(textMatrixTracker.getTextMatrix().isIdentity());
    }

    @Test
    void testNegativeTranslation() {
        textMatrixTracker.translate(-10.0f, -20.0f);

        Point2D origin = new Point2D.Float(0.0f, 0.0f);
        Point2D transformed = textMatrixTracker.getTextMatrix().transform(origin, null);

        assertEquals(-10.0, transformed.getX(), 0.001);
        assertEquals(-20.0, transformed.getY(), 0.001);
    }

    @Test
    void testSetMatrixOverwritesPreviousState() {
        // Apply some translations
        textMatrixTracker.translate(100.0f, 200.0f);

        // Set new matrix should overwrite previous state
        AffineTransform newMatrix = AffineTransform.getTranslateInstance(5.0, 10.0);
        textMatrixTracker.setTextMatrix(newMatrix);

        Point2D origin = new Point2D.Float(0.0f, 0.0f);
        Point2D transformed = textMatrixTracker.getTextMatrix().transform(origin, null);

        assertEquals(5.0, transformed.getX(), 0.001);
        assertEquals(10.0, transformed.getY(), 0.001);
    }

    @Test
    void testReset() {
        // Apply some transformations
        textMatrixTracker.translate(50.0f, 100.0f);
        assertFalse(textMatrixTracker.getTextMatrix().isIdentity());

        // Reset should restore identity matrix
        textMatrixTracker.reset();
        assertTrue(textMatrixTracker.getTextMatrix().isIdentity());
    }

    @Test
    void testMoveToNextLine() {
        float leading = 12.0f;

        textMatrixTracker.moveToNextLine(leading);

        Point2D origin = new Point2D.Float(0.0f, 0.0f);
        Point2D transformed = textMatrixTracker.getTextMatrix().transform(origin, null);

        assertEquals(0.0, transformed.getX(), 0.001);
        assertEquals(-leading, transformed.getY(), 0.001); // Negative because PDF coordinates
    }
}
