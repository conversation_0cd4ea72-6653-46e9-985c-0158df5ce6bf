package com.tfc.model;

import java.awt.image.BufferedImage;

public class Image extends Element {
    public final String id;
    public Size size;
    public BufferedImage bufferedImage;

    public Image() {
        this.id = ElementIdGenerator.generateId("IMAGE");
    }

    public String getId() {
        return id;
    }

    @Override
    public String toString() {
        return "Image{" +
                "id='" + id + '\'' +
                ", size=" + size +
                ", position=" + position +
                '}';
    }
}
