package com.tfc.model;

import org.jetbrains.annotations.NotNull;

/**
 * Represents a line segment in a PDF document.
 * A line segment is defined by its start and end points, and a stroke color.
 */
public record PDFLineElement(String id, float x1, float y1, float x2, float y2, RGBA strokeColor) {

    public PDFLineElement(float x1, float y1, float x2, float y2, RGBA strokeColor) {
        this(ElementIdGenerator.generateId("LINE"), x1, y1, x2, y2, strokeColor);
    }

    @Override
    public @NotNull String toString() {
        return String.format("PDFLineElement{id='%s', start=[x=%.2f, y=%.2f], end=[x=%.2f, y=%.2f], stroke=%s}",
                id, x1, y1, x2, y2, strokeColor);
    }
}