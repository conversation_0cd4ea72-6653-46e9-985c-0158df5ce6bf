package com.tfc.model;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Utility class for generating unique IDs for PDF elements.
 * Thread-safe ID generation for debugging and identification purposes.
 */
public class ElementIdGenerator {
    
    private static final AtomicLong counter = new AtomicLong(1);
    
    /**
     * Generates a unique ID with the specified prefix.
     * 
     * @param prefix the prefix for the ID (e.g., "TEXT", "SHAPE", "LINE", "IMAGE")
     * @return a unique ID string in the format "PREFIX_000001"
     */
    public static String generateId(String prefix) {
        long id = counter.getAndIncrement();
        return String.format("%s_%06d", prefix, id);
    }
    
    /**
     * Resets the counter to 1. This method is primarily intended for testing purposes.
     * Use with caution in production code as it may cause ID collisions.
     */
    public static void resetCounter() {
        counter.set(1);
    }
    
    /**
     * Gets the current counter value without incrementing it.
     * This method is primarily intended for testing purposes.
     * 
     * @return the current counter value
     */
    public static long getCurrentCounter() {
        return counter.get();
    }
}
