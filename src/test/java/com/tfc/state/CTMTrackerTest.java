package com.tfc.state;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class CTMTrackerTest {

    private CTMTracker ctmTracker;

    @BeforeEach
    void setUp() {
        ctmTracker = new CTMTracker();
    }

    @Test
    void testInitialState() {
        double[] ctm = ctmTracker.getCTM();
        
        // Initial CTM should be identity matrix
        assertArrayEquals(new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0}, ctm, 0.001);
    }

    @Test
    void testSetCTM() {
        double[] newCTM = {2.0, 0.5, -0.5, 1.5, 10.0, 20.0};
        
        ctmTracker.setCTM(newCTM);
        
        assertArrayEquals(newCTM, ctmTracker.getCTM(), 0.001);
    }

    @Test
    void testSetCTMWithZeros() {
        double[] newCTM = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
        
        ctmTracker.setCTM(newCTM);
        
        assertArrayEquals(newCTM, ctmTracker.getCTM(), 0.001);
    }

    @Test
    void testMultiplyCTMWithIdentity() {
        // Multiplying by identity matrix should not change the CTM
        ctmTracker.multiplyCTM(1.0, 0.0, 0.0, 1.0, 0.0, 0.0);
        
        double[] expected = {1.0, 0.0, 0.0, 1.0, 0.0, 0.0};
        assertArrayEquals(expected, ctmTracker.getCTM(), 0.001);
    }

    @Test
    void testMultiplyCTMWithTranslation() {
        // Apply translation (10, 20)
        ctmTracker.multiplyCTM(1.0, 0.0, 0.0, 1.0, 10.0, 20.0);
        
        double[] expected = {1.0, 0.0, 0.0, 1.0, 10.0, 20.0};
        assertArrayEquals(expected, ctmTracker.getCTM(), 0.001);
    }

    @Test
    void testMultiplyCTMWithScale() {
        // Apply scale (2x, 3x)
        ctmTracker.multiplyCTM(2.0, 0.0, 0.0, 3.0, 0.0, 0.0);
        
        double[] expected = {2.0, 0.0, 0.0, 3.0, 0.0, 0.0};
        assertArrayEquals(expected, ctmTracker.getCTM(), 0.001);
    }

    @Test
    void testMultiplyCTMWithRotation() {
        // Apply 90-degree rotation (cos(90°)=0, sin(90°)=1)
        ctmTracker.multiplyCTM(0.0, 1.0, -1.0, 0.0, 0.0, 0.0);
        
        double[] expected = {0.0, 1.0, -1.0, 0.0, 0.0, 0.0};
        assertArrayEquals(expected, ctmTracker.getCTM(), 0.001);
    }

    @Test
    void testMultipleCTMChaining() {
        // Apply scale first, then translation
        ctmTracker.multiplyCTM(2.0, 0.0, 0.0, 2.0, 0.0, 0.0); // Scale by 2
        ctmTracker.multiplyCTM(1.0, 0.0, 0.0, 1.0, 10.0, 20.0); // Translate by (10, 20)

        // When multiplying matrices, the translation gets transformed by the scale
        // newE = 2*10 + 0*20 + 0 = 20, newF = 0*10 + 2*20 + 0 = 40
        double[] expected = {2.0, 0.0, 0.0, 2.0, 20.0, 40.0};
        assertArrayEquals(expected, ctmTracker.getCTM(), 0.001);
    }

    @Test
    void testMultipleCTMComplexTransformation() {
        // Set initial CTM to a non-identity matrix
        ctmTracker.setCTM(new double[]{2.0, 0.0, 0.0, 3.0, 5.0, 10.0});

        // Apply another transformation
        ctmTracker.multiplyCTM(1.0, 0.0, 0.0, 1.0, 2.0, 3.0);

        // Expected result: newE = 2*2 + 0*3 + 5 = 9, newF = 0*2 + 3*3 + 10 = 19
        double[] expected = {2.0, 0.0, 0.0, 3.0, 9.0, 19.0};
        assertArrayEquals(expected, ctmTracker.getCTM(), 0.001);
    }

    @Test
    void testTransformPoint() {
        // Set up a scale and translation transformation
        ctmTracker.setCTM(new double[]{2.0, 0.0, 0.0, 3.0, 10.0, 20.0});
        
        double[] result = ctmTracker.transformPoint(5.0, 7.0);
        
        // Expected: x' = 2*5 + 0*7 + 10 = 20, y' = 0*5 + 3*7 + 20 = 41
        assertArrayEquals(new double[]{20.0, 41.0}, result, 0.001);
    }

    @Test
    void testTransformPointWithIdentity() {
        double[] result = ctmTracker.transformPoint(10.0, 15.0);
        
        // With identity matrix, point should remain unchanged
        assertArrayEquals(new double[]{10.0, 15.0}, result, 0.001);
    }

    @Test
    void testTransformPointWithZeroPoint() {
        ctmTracker.setCTM(new double[]{1.0, 0.0, 0.0, 1.0, 5.0, 10.0});
        
        double[] result = ctmTracker.transformPoint(0.0, 0.0);
        
        // Should return the translation values
        assertArrayEquals(new double[]{5.0, 10.0}, result, 0.001);
    }

    @Test
    void testCopy() {
        // Set up a custom CTM
        double[] originalCTM = {2.0, 0.5, -0.5, 1.5, 10.0, 20.0};
        ctmTracker.setCTM(originalCTM);
        
        CTMTracker copy = ctmTracker.copy();
        
        // Copy should have the same CTM values
        assertArrayEquals(originalCTM, copy.getCTM(), 0.001);
        
        // But should be a different object
        assertNotSame(ctmTracker, copy);
        
        // Modifying the copy should not affect the original
        copy.setCTM(new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0});
        assertArrayEquals(originalCTM, ctmTracker.getCTM(), 0.001);
    }

    @Test
    void testCopyWithDefaultCTM() {
        CTMTracker copy = ctmTracker.copy();
        
        // Copy should have the same identity matrix
        assertArrayEquals(new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0}, copy.getCTM(), 0.001);
        assertNotSame(ctmTracker, copy);
    }

    @Test
    void testMatrixMultiplicationMath() {
        // Test the mathematical correctness of matrix multiplication
        // Matrix A = [a, b, c, d, e, f] represents:
        // [a c e]
        // [b d f]
        // [0 0 1]
        
        ctmTracker.setCTM(new double[]{1.0, 2.0, 3.0, 4.0, 5.0, 6.0});
        ctmTracker.multiplyCTM(7.0, 8.0, 9.0, 10.0, 11.0, 12.0);
        
        // Manual calculation:
        // newA = 1*7 + 3*8 = 31
        // newB = 2*7 + 4*8 = 46
        // newC = 1*9 + 3*10 = 39
        // newD = 2*9 + 4*10 = 58
        // newE = 1*11 + 3*12 + 5 = 52
        // newF = 2*11 + 4*12 + 6 = 76
        
        double[] expected = {31.0, 46.0, 39.0, 58.0, 52.0, 76.0};
        assertArrayEquals(expected, ctmTracker.getCTM(), 0.001);
    }
}
