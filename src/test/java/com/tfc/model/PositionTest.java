package com.tfc.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class PositionTest {

    @Test
    void testToString() {
        Position position = new Position();
        position.x = 10.5f;
        position.y = 20.7f;
        
        String expected = "Position{x=10.5, y=20.7}";
        assertEquals(expected, position.toString());
    }

    @Test
    void testToStringWithZeroValues() {
        Position position = new Position();
        position.x = 0.0f;
        position.y = 0.0f;
        
        String expected = "Position{x=0.0, y=0.0}";
        assertEquals(expected, position.toString());
    }

    @Test
    void testToStringWithNegativeValues() {
        Position position = new Position();
        position.x = -5.3f;
        position.y = -12.8f;
        
        String expected = "Position{x=-5.3, y=-12.8}";
        assertEquals(expected, position.toString());
    }

    @Test
    void testFieldsArePublic() {
        Position position = new Position();
        
        // Test that fields can be accessed and modified directly
        position.x = 100.0f;
        position.y = 200.0f;
        
        assertEquals(100.0f, position.x);
        assertEquals(200.0f, position.y);
    }
}
