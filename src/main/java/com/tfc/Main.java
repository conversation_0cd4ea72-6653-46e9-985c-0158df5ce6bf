package com.tfc;

import java.io.File;
import java.net.URL;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.joran.JoranConfigurator;
import ch.qos.logback.core.joran.spi.JoranException;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;

/**
 * Main class that demonstrates the usage of PdfBoxPdfAnalyzer.
 */
@Command(name = "pdf-engine", mixinStandardHelpOptions = true,
        description = "Processes PDF files using TheFamousPDFStreamEngine.")
public class Main implements Runnable {

    static {
        // Default to standard logback.xml
        configureLogback(false);
    }

    private static final Logger logger = LoggerFactory.getLogger(Main.class);

    @Option(names = {"-d", "--debug"}, description = "Enable debug mode with verbose logging")
    private boolean debug = false;

    @Parameters(index = "0", description = "The PDF file to process")
    private File pdfFile;

    @Option(names = {"-o", "--output"}, description = "Output PDF file path")
    private File outputFile = new File("output.pdf");

    /**
     * Configure Logback with either the standard or debug configuration.
     *
     * @param debugMode if true, use the debug configuration
     */
    private static void configureLogback(boolean debugMode) {
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        try {
            JoranConfigurator configurator = new JoranConfigurator();
            configurator.setContext(context);
            context.reset();

            String logbackConfig = debugMode ? "logback-debug.xml" : "logback.xml";
            URL configUrl = Main.class.getClassLoader().getResource(logbackConfig);
            if (configUrl != null) {
                configurator.doConfigure(configUrl);
            } else {
                System.err.println("Could not find " + logbackConfig + " in classpath");
            }
        } catch (JoranException je) {
            // StatusPrinter.printInCaseOfErrorsOrWarnings(context);
            System.err.println("Error configuring Logback: " + je.getMessage());
        }
    }

    @Override
    public void run() {
        // Reconfigure logging if debug mode is enabled
        if (debug) {
            configureLogback(true);
            logger.info("Debug mode enabled with verbose logging");
        }

        if (!pdfFile.exists() || !pdfFile.isFile()) {
            logger.error("Error: The specified PDF file does not exist or is not a file.");
            return;
        }

        try {
            TheFamousPDFStreamEngine streamEngine = new TheFamousPDFStreamEngine();

            try (PDDocument document = Loader.loadPDF(pdfFile)) {
                for (PDPage pdPage : document.getPages()) {
                    streamEngine.processPage(pdPage);
                }
            }
            streamEngine.buildFinalPDF(outputFile);
            logger.info("PDF processing completed. Output saved to: {}", outputFile.getAbsolutePath());
        } catch (Exception e) {
            logger.error("Error parsing PDF: {}", e.getMessage());
        }
    }

    public static void main(String[] args) {
        int exitCode = new CommandLine(new Main()).execute(args);
        System.exit(exitCode);
    }
}
