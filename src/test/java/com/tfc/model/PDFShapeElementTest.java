package com.tfc.model;

import org.junit.jupiter.api.Test;
import java.awt.geom.Rectangle2D;

import static org.junit.jupiter.api.Assertions.*;

class PDFShapeElementTest {

    @Test
    void testRecordCreation() {
        float x = 10.5f;
        float y = 20.7f;
        float width = 100.0f;
        float height = 50.0f;
        RGBA fillColor = new RGBA(255, 0, 0, 255);
        RGBA strokeColor = new RGBA(0, 255, 0, 255);

        PDFShapeElement shape = new PDFShapeElement(x, y, width, height, fillColor, strokeColor);

        assertEquals(x, shape.x());
        assertEquals(y, shape.y());
        assertEquals(width, shape.width());
        assertEquals(height, shape.height());
        assertSame(fillColor, shape.fillColor());
        assertSame(strokeColor, shape.strokeColor());
    }

    @Test
    void testRecordWithNullColors() {
        PDFShapeElement shape = new PDFShapeElement(0.0f, 0.0f, 10.0f, 10.0f, null, null);

        assertNull(shape.fillColor());
        assertNull(shape.strokeColor());
    }

    @Test
    void testGetRect() {
        float x = 15.0f;
        float y = 25.0f;
        float width = 80.0f;
        float height = 60.0f;

        PDFShapeElement shape = new PDFShapeElement(x, y, width, height, null, null);
        Rectangle2D.Float rect = shape.getRect();

        assertEquals(x, rect.x, 0.001f);
        assertEquals(y, rect.y, 0.001f);
        assertEquals(width, rect.width, 0.001f);
        assertEquals(height, rect.height, 0.001f);
    }

    @Test
    void testGetRectWithZeroValues() {
        PDFShapeElement shape = new PDFShapeElement(0.0f, 0.0f, 0.0f, 0.0f, null, null);
        Rectangle2D.Float rect = shape.getRect();

        assertEquals(0.0f, rect.x, 0.001f);
        assertEquals(0.0f, rect.y, 0.001f);
        assertEquals(0.0f, rect.width, 0.001f);
        assertEquals(0.0f, rect.height, 0.001f);
    }

    @Test
    void testGetRectWithNegativeValues() {
        PDFShapeElement shape = new PDFShapeElement(-10.0f, -20.0f, 30.0f, 40.0f, null, null);
        Rectangle2D.Float rect = shape.getRect();

        assertEquals(-10.0f, rect.x, 0.001f);
        assertEquals(-20.0f, rect.y, 0.001f);
        assertEquals(30.0f, rect.width, 0.001f);
        assertEquals(40.0f, rect.height, 0.001f);
    }

    @Test
    void testToString() {
        RGBA fillColor = new RGBA(255, 128, 64, 255);
        RGBA strokeColor = new RGBA(0, 0, 255, 128);
        PDFShapeElement shape = new PDFShapeElement(12.34f, 56.78f, 90.12f, 34.56f, fillColor, strokeColor);

        String result = shape.toString();

        assertTrue(result.contains("PDFShapeElement{"));
        assertTrue(result.contains("x=12.34"));
        assertTrue(result.contains("y=56.78"));
        assertTrue(result.contains("w=90.12"));
        assertTrue(result.contains("h=34.56"));
        assertTrue(result.contains("fill="));
        assertTrue(result.contains("stroke="));
    }

    @Test
    void testToStringWithNullColors() {
        PDFShapeElement shape = new PDFShapeElement(1.0f, 2.0f, 3.0f, 4.0f, null, null);

        String result = shape.toString();

        assertTrue(result.contains("PDFShapeElement{"));
        assertTrue(result.contains("x=1.00"));
        assertTrue(result.contains("y=2.00"));
        assertTrue(result.contains("w=3.00"));
        assertTrue(result.contains("h=4.00"));
        assertTrue(result.contains("fill=null"));
        assertTrue(result.contains("stroke=null"));
    }

    @Test
    void testRecordEquality() {
        RGBA color1 = new RGBA(255, 0, 0, 255);
        RGBA color2 = new RGBA(0, 255, 0, 255);

        PDFShapeElement shape1 = new PDFShapeElement(10.0f, 20.0f, 30.0f, 40.0f, color1, color2);
        PDFShapeElement shape2 = new PDFShapeElement(10.0f, 20.0f, 30.0f, 40.0f, color1, color2);

        // Since each shape has a unique ID, they should not be equal
        assertNotEquals(shape1, shape2);
        assertNotEquals(shape1.hashCode(), shape2.hashCode());

        // But they should have different IDs
        assertNotEquals(shape1.id(), shape2.id());

        // And same other properties
        assertEquals(shape1.x(), shape2.x());
        assertEquals(shape1.y(), shape2.y());
        assertEquals(shape1.width(), shape2.width());
        assertEquals(shape1.height(), shape2.height());
        assertSame(shape1.fillColor(), shape2.fillColor());
        assertSame(shape1.strokeColor(), shape2.strokeColor());
    }

    @Test
    void testRecordInequality() {
        RGBA color = new RGBA(255, 0, 0, 255);

        PDFShapeElement shape1 = new PDFShapeElement(10.0f, 20.0f, 30.0f, 40.0f, color, null);
        PDFShapeElement shape2 = new PDFShapeElement(11.0f, 20.0f, 30.0f, 40.0f, color, null);

        assertNotEquals(shape1, shape2);
    }

    @Test
    void testRecordImmutability() {
        RGBA fillColor = new RGBA(255, 0, 0, 255);
        PDFShapeElement shape = new PDFShapeElement(10.0f, 20.0f, 30.0f, 40.0f, fillColor, null);

        // Record fields should be final and immutable
        assertEquals(10.0f, shape.x());
        assertEquals(20.0f, shape.y());
        assertEquals(30.0f, shape.width());
        assertEquals(40.0f, shape.height());
        assertSame(fillColor, shape.fillColor());
    }

    @Test
    void testIdGeneration() {
        PDFShapeElement shape1 = new PDFShapeElement(0.0f, 0.0f, 10.0f, 10.0f, null, null);
        PDFShapeElement shape2 = new PDFShapeElement(0.0f, 0.0f, 10.0f, 10.0f, null, null);

        // Each shape should have a unique ID
        assertNotNull(shape1.id());
        assertNotNull(shape2.id());
        assertNotEquals(shape1.id(), shape2.id());

        // IDs should follow the expected format
        assertTrue(shape1.id().startsWith("SHAPE_"));
        assertTrue(shape2.id().startsWith("SHAPE_"));
    }

    @Test
    void testToStringIncludesId() {
        PDFShapeElement shape = new PDFShapeElement(12.34f, 56.78f, 90.12f, 34.56f, null, null);
        String result = shape.toString();

        assertTrue(result.contains("id="));
        assertTrue(result.contains(shape.id()));
    }
}
