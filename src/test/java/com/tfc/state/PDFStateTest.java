package com.tfc.state;

import com.tfc.model.*;
import org.apache.pdfbox.cos.*;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class PDFStateTest {

    private PDFState pdfState;
    private PDPage page;

    @BeforeEach
    void setUp() {
        page = new PDPage();
        pdfState = new PDFState(page);
    }

    // ========== CONSTRUCTOR AND INITIALIZATION TESTS ==========

    @Test
    void testConstructorInitializesPage() {
        assertSame(page, pdfState.page);
    }

    @Test
    void testConstructorInitializesDocumentStructure() {
        PDFPageStructure structure = pdfState.getPDFDocumentStructure();
        assertNotNull(structure);
        assertTrue(structure.getElements().isEmpty());
        assertTrue(structure.getShapes().isEmpty());
        assertTrue(structure.getLines().isEmpty());
        assertTrue(structure.getImages().isEmpty());
    }

    @Test
    void testConstructorWithNullPage() {
        // Should not throw exception, but page will be null
        PDFState stateWithNullPage = new PDFState(null);
        assertNull(stateWithNullPage.page);
        assertNotNull(stateWithNullPage.getPDFDocumentStructure());
    }

    // ========== GRAPHICS STATE METHODS TESTS ==========

    @Test
    void testSaveAndRestoreGraphicsState() {
        // Modify graphics state first
        List<COSBase> cmOperands = Arrays.asList(
                new COSFloat(2.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(2.0f),
                new COSFloat(10.0f), new COSFloat(20.0f)
        );
        pdfState.addCM(cmOperands);

        // Save state
        pdfState.saveGraphicsState();

        // Modify state again
        List<COSBase> cmOperands2 = Arrays.asList(
                new COSFloat(3.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(3.0f),
                new COSFloat(5.0f), new COSFloat(15.0f)
        );
        pdfState.addCM(cmOperands2);

        // Restore state
        pdfState.restoreGraphicsState();

        // State should be restored (this is a basic test - more detailed testing would require access to internal state)
        assertDoesNotThrow(() -> pdfState.restoreGraphicsState());
    }

    @Test
    void testAddCM() {
        List<COSBase> cmOperands = Arrays.asList(
                new COSFloat(1.5f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(1.5f),
                new COSFloat(100.0f), new COSFloat(200.0f)
        );

        assertDoesNotThrow(() -> pdfState.addCM(cmOperands));
    }

    @Test
    void testAddCMWithNullOperands() {
        assertDoesNotThrow(() -> pdfState.addCM(null));
    }

    @Test
    void testMultipleSaveRestoreOperations() {
        // Test nested save/restore operations
        pdfState.saveGraphicsState();
        pdfState.saveGraphicsState();
        pdfState.saveGraphicsState();

        pdfState.restoreGraphicsState();
        pdfState.restoreGraphicsState();
        pdfState.restoreGraphicsState();

        // Should not throw exception even if stack is empty
        assertDoesNotThrow(() -> pdfState.restoreGraphicsState());
    }

    // ========== TEXT STATE METHODS TESTS ==========

    @Test
    void testEnterAndExitTextBlock() {
        assertDoesNotThrow(() -> pdfState.enterTextBlock());
        assertDoesNotThrow(() -> pdfState.exitTextBlock());
    }

    @Test
    void testSetTextMatrix() {
        List<COSBase> textMatrix = Arrays.asList(
                new COSFloat(1.0f), new COSFloat(0.0f),
                new COSFloat(0.0f), new COSFloat(1.0f),
                new COSFloat(50.0f), new COSFloat(100.0f)
        );

        assertDoesNotThrow(() -> pdfState.setTextMatrix(textMatrix));
    }

    @Test
    void testSetTextMatrixWithNullOperands() {
        assertDoesNotThrow(() -> pdfState.setTextMatrix(null));
    }

    @Test
    void testSetFontName() {
        COSName fontName = COSName.getPDFName("Helvetica");
        assertDoesNotThrow(() -> pdfState.setFontName(fontName));
    }

    @Test
    void testSetFontNameWithNull() {
        assertDoesNotThrow(() -> pdfState.setFontName(null));
    }

    @Test
    void testSetFontSize() {
        COSNumber fontSize = new COSFloat(12.0f);
        assertDoesNotThrow(() -> pdfState.setFontSize(fontSize));
    }

    @Test
    void testSetFontSizeWithNull() {
        assertDoesNotThrow(() -> pdfState.setFontSize(null));
    }

    @Test
    void testAddText() {
        COSString cosString = new COSString("Hello World");
        assertDoesNotThrow(() -> pdfState.addText(cosString));
    }

    @Test
    void testAddTextWithNull() {
        assertDoesNotThrow(() -> pdfState.addText(null));
    }

    @Test
    void testSetCurrentFont() {
        PDType1Font font = new PDType1Font(Standard14Fonts.FontName.HELVETICA);
        assertDoesNotThrow(() -> pdfState.setCurrentFont(font));
    }

    @Test
    void testSetCurrentFontWithNull() {
        assertDoesNotThrow(() -> pdfState.setCurrentFont(null));
    }

    @Test
    void testTranslateTextPosition() {
        assertDoesNotThrow(() -> pdfState.translateTextPosition(10.0f, 20.0f));
        assertDoesNotThrow(() -> pdfState.translateTextPosition(-5.0f, -15.0f));
        assertDoesNotThrow(() -> pdfState.translateTextPosition(0.0f, 0.0f));
    }

    // ========== COLOR METHODS TESTS ==========

    @Test
    void testSetAndGetNonStrokingColor() {
        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(1.0f),  // Red
                new COSFloat(0.5f),  // Green
                new COSFloat(0.0f)   // Blue
        );

        pdfState.setNonStrokingColor(colorOperands);
        RGBA color = pdfState.getNonStrokingColor();

        assertNotNull(color);
        assertEquals(255, color.r);
        assertEquals(128, color.g);
        assertEquals(0, color.b);
        assertEquals(255, color.a);
    }

    @Test
    void testSetNonStrokingColorWithNull() {
        assertDoesNotThrow(() -> pdfState.setNonStrokingColor(null));
    }

    @Test
    void testGetNonStrokingColorInitiallyNull() {
        assertNull(pdfState.getNonStrokingColor());
    }

    @Test
    void testSetAndGetStrokingColor() {
        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(0.2f),  // Red
                new COSFloat(0.7f),  // Green
                new COSFloat(0.9f)   // Blue
        );

        pdfState.setStrokingColor(colorOperands);
        RGBA color = pdfState.getStrokingColor();

        assertNotNull(color);
        assertEquals(51, color.r);
        assertEquals(179, color.g);
        assertEquals(230, color.b);
        assertEquals(255, color.a);
    }

    @Test
    void testSetStrokingColorWithNull() {
        assertDoesNotThrow(() -> pdfState.setStrokingColor(null));
    }

    @Test
    void testGetStrokingColorInitiallyNull() {
        assertNull(pdfState.getStrokingColor());
    }

    @Test
    void testSetAndGetStrokingAlpha() {
        float alpha = 0.75f;
        pdfState.setStrokingAlpha(alpha);
        assertEquals(alpha, pdfState.getStrokingAlpha());
    }

    @Test
    void testSetAndGetNonStrokingAlpha() {
        float alpha = 0.25f;
        pdfState.setNonStrokingAlpha(alpha);
        assertEquals(alpha, pdfState.getNonStrokingAlpha());
    }

    @Test
    void testInitialAlphaValues() {
        assertEquals(1.0f, pdfState.getStrokingAlpha());
        assertEquals(1.0f, pdfState.getNonStrokingAlpha());
    }

    @Test
    void testAlphaExtremeValues() {
        pdfState.setStrokingAlpha(0.0f);
        pdfState.setNonStrokingAlpha(1.0f);

        assertEquals(0.0f, pdfState.getStrokingAlpha());
        assertEquals(1.0f, pdfState.getNonStrokingAlpha());
    }

    // ========== DOCUMENT STRUCTURE METHODS TESTS ==========

    @Test
    void testGetPDFDocumentStructureReturnsSameInstance() {
        PDFPageStructure structure1 = pdfState.getPDFDocumentStructure();
        PDFPageStructure structure2 = pdfState.getPDFDocumentStructure();

        assertSame(structure1, structure2);
    }

    @Test
    void testGetPDFDocumentStructureNotNull() {
        assertNotNull(pdfState.getPDFDocumentStructure());
    }

    // ========== CLIPPING PATH METHODS TESTS ==========

    @Test
    void testInitialClippingPathState() {
        assertFalse(pdfState.hasClippingPath());
        assertFalse(pdfState.isEvenOddRule());
    }

    @Test
    void testSetClippingPath() {
        pdfState.setClippingPath();
        assertTrue(pdfState.hasClippingPath());
        assertFalse(pdfState.isEvenOddRule());
    }

    @Test
    void testSetClippingPathEvenOdd() {
        pdfState.setClippingPathEvenOdd();
        assertTrue(pdfState.hasClippingPath());
        assertTrue(pdfState.isEvenOddRule());
    }

    @Test
    void testIntersectsClippingPathWithoutClippingPath() {
        Rectangle2D.Float rect = new Rectangle2D.Float(10.0f, 20.0f, 30.0f, 40.0f);
        assertTrue(pdfState.intersectsClippingPath(rect));
    }

    @Test
    void testIntersectsClippingPathWithNull() {
        assertDoesNotThrow(() -> pdfState.intersectsClippingPath(null));
    }
