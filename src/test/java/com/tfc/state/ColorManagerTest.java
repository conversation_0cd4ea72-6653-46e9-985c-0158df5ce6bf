package com.tfc.state;

import com.tfc.model.RGBA;
import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.cos.COSFloat;
import org.apache.pdfbox.cos.COSInteger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ColorManagerTest {

    private ColorManager colorManager;

    @BeforeEach
    void setUp() {
        colorManager = new ColorManager();
    }

    @Test
    void testInitialState() {
        assertNull(colorManager.getNonStrokingColor());
        assertNull(colorManager.getStrokingColor());
        assertEquals(1.0f, colorManager.getStrokingAlpha());
        assertEquals(1.0f, colorManager.getNonStrokingAlpha());
    }

    @Test
    void testSetNonStrokingColor() {
        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(1.0f),  // Red = 1.0 (255)
                new COSFloat(0.5f),  // Green = 0.5 (128)
                new COSFloat(0.0f)   // Blue = 0.0 (0)
        );

        colorManager.setNonStrokingColor(colorOperands);

        RGBA color = colorManager.getNonStrokingColor();
        assertNotNull(color);
        assertEquals(255, color.r);
        assertEquals(128, color.g);
        assertEquals(0, color.b);
        assertEquals(255, color.a); // Default alpha is 1.0
    }

    @Test
    void testSetNonStrokingColorWithCustomAlpha() {
        colorManager.setNonStrokingAlpha(0.5f);

        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(0.8f),
                new COSFloat(0.6f),
                new COSFloat(0.4f)
        );

        colorManager.setNonStrokingColor(colorOperands);

        RGBA color = colorManager.getNonStrokingColor();
        assertNotNull(color);
        assertEquals(204, color.r); // 0.8 * 255
        assertEquals(153, color.g); // 0.6 * 255
        assertEquals(102, color.b); // 0.4 * 255
        assertEquals(128, color.a); // 0.5 * 255
    }

    @Test
    void testSetStrokingColor() {
        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(0.2f),  // Red = 0.2 (51)
                new COSFloat(0.7f),  // Green = 0.7 (179)
                new COSFloat(0.9f)   // Blue = 0.9 (230)
        );

        colorManager.setStrokingColor(colorOperands);

        RGBA color = colorManager.getStrokingColor();
        assertNotNull(color);
        assertEquals(51, color.r);
        assertEquals(179, color.g);
        assertEquals(230, color.b);
        assertEquals(255, color.a); // Default alpha is 1.0
    }

    @Test
    void testSetStrokingColorWithCustomAlpha() {
        colorManager.setStrokingAlpha(0.3f);

        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(1.0f),
                new COSFloat(0.0f),
                new COSFloat(0.5f)
        );

        colorManager.setStrokingColor(colorOperands);

        RGBA color = colorManager.getStrokingColor();
        assertNotNull(color);
        assertEquals(255, color.r);
        assertEquals(0, color.g);
        assertEquals(128, color.b);
        assertEquals(77, color.a); // 0.3 * 255
    }

    @Test
    void testSetStrokingAlpha() {
        float alpha = 0.75f;
        colorManager.setStrokingAlpha(alpha);

        assertEquals(alpha, colorManager.getStrokingAlpha());
    }

    @Test
    void testSetNonStrokingAlpha() {
        float alpha = 0.25f;
        colorManager.setNonStrokingAlpha(alpha);

        assertEquals(alpha, colorManager.getNonStrokingAlpha());
    }

    @Test
    void testSetAlphaZero() {
        colorManager.setStrokingAlpha(0.0f);
        colorManager.setNonStrokingAlpha(0.0f);

        assertEquals(0.0f, colorManager.getStrokingAlpha());
        assertEquals(0.0f, colorManager.getNonStrokingAlpha());
    }

    @Test
    void testSetAlphaOne() {
        colorManager.setStrokingAlpha(1.0f);
        colorManager.setNonStrokingAlpha(1.0f);

        assertEquals(1.0f, colorManager.getStrokingAlpha());
        assertEquals(1.0f, colorManager.getNonStrokingAlpha());
    }

    @Test
    void testColorConversionWithIntegerValues() {
        List<COSBase> colorOperands = Arrays.asList(
                COSInteger.ONE,  // Should be treated as 1.0
                COSInteger.ZERO,  // Should be treated as 0.0
                new COSFloat(0.5f)
        );

        colorManager.setNonStrokingColor(colorOperands);

        RGBA color = colorManager.getNonStrokingColor();
        assertNotNull(color);
        assertEquals(255, color.r);
        assertEquals(0, color.g);
        assertEquals(128, color.b);
        assertEquals(255, color.a);
    }

    @Test
    void testColorConversionWithExtremeValues() {
        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(0.0f),
                new COSFloat(1.0f),
                new COSFloat(0.5f)
        );

        colorManager.setStrokingColor(colorOperands);

        RGBA color = colorManager.getStrokingColor();
        assertNotNull(color);
        assertEquals(0, color.r);
        assertEquals(255, color.g);
        assertEquals(128, color.b);
        assertEquals(255, color.a);
    }

    @Test
    void testColorRounding() {
        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(0.333f),  // Should round to 85
                new COSFloat(0.666f),  // Should round to 170
                new COSFloat(0.999f)   // Should round to 255
        );

        colorManager.setNonStrokingColor(colorOperands);

        RGBA color = colorManager.getNonStrokingColor();
        assertNotNull(color);
        assertEquals(85, color.r);   // Math.round(0.333 * 255) = 85
        assertEquals(170, color.g);  // Math.round(0.666 * 255) = 170
        assertEquals(255, color.b);  // Math.round(0.999 * 255) = 255
    }

    @Test
    void testAlphaRounding() {
        colorManager.setStrokingAlpha(0.333f);
        colorManager.setNonStrokingAlpha(0.666f);

        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(1.0f),
                new COSFloat(1.0f),
                new COSFloat(1.0f)
        );

        colorManager.setStrokingColor(colorOperands);
        colorManager.setNonStrokingColor(colorOperands);

        RGBA strokingColor = colorManager.getStrokingColor();
        RGBA nonStrokingColor = colorManager.getNonStrokingColor();

        assertEquals(85, strokingColor.a);    // Math.round(0.333 * 255) = 85
        assertEquals(170, nonStrokingColor.a); // Math.round(0.666 * 255) = 170
    }

    @Test
    void testIndependentColorManagement() {
        // Set different colors for stroking and non-stroking
        List<COSBase> strokingOperands = Arrays.asList(
                new COSFloat(1.0f),
                new COSFloat(0.0f),
                new COSFloat(0.0f)
        );

        List<COSBase> nonStrokingOperands = Arrays.asList(
                new COSFloat(0.0f),
                new COSFloat(1.0f),
                new COSFloat(0.0f)
        );

        colorManager.setStrokingColor(strokingOperands);
        colorManager.setNonStrokingColor(nonStrokingOperands);

        RGBA strokingColor = colorManager.getStrokingColor();
        RGBA nonStrokingColor = colorManager.getNonStrokingColor();

        // Stroking should be red
        assertEquals(255, strokingColor.r);
        assertEquals(0, strokingColor.g);
        assertEquals(0, strokingColor.b);

        // Non-stroking should be green
        assertEquals(0, nonStrokingColor.r);
        assertEquals(255, nonStrokingColor.g);
        assertEquals(0, nonStrokingColor.b);
    }

    @Test
    void testIndependentAlphaManagement() {
        colorManager.setStrokingAlpha(0.2f);
        colorManager.setNonStrokingAlpha(0.8f);

        List<COSBase> colorOperands = Arrays.asList(
                new COSFloat(1.0f),
                new COSFloat(1.0f),
                new COSFloat(1.0f)
        );

        colorManager.setStrokingColor(colorOperands);
        colorManager.setNonStrokingColor(colorOperands);

        RGBA strokingColor = colorManager.getStrokingColor();
        RGBA nonStrokingColor = colorManager.getNonStrokingColor();

        assertEquals(51, strokingColor.a);    // 0.2 * 255
        assertEquals(204, nonStrokingColor.a); // 0.8 * 255
    }

    @Test
    void testOverwritingColors() {
        // Set initial colors
        List<COSBase> initialOperands = Arrays.asList(
                new COSFloat(1.0f),
                new COSFloat(0.0f),
                new COSFloat(0.0f)
        );

        colorManager.setStrokingColor(initialOperands);

        // Overwrite with new colors
        List<COSBase> newOperands = Arrays.asList(
                new COSFloat(0.0f),
                new COSFloat(0.0f),
                new COSFloat(1.0f)
        );

        colorManager.setStrokingColor(newOperands);

        RGBA color = colorManager.getStrokingColor();
        assertEquals(0, color.r);
        assertEquals(0, color.g);
        assertEquals(255, color.b);
    }
}
