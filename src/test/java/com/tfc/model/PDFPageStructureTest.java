package com.tfc.model;

import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.awt.geom.Point2D;
import java.awt.image.BufferedImage;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class PDFPageStructureTest {

    private PDFPageStructure pageStructure;

    @BeforeEach
    void setUp() {
        pageStructure = new PDFPageStructure();
    }

    @Test
    void testInitialState() {
        assertTrue(pageStructure.getElements().isEmpty());
        assertTrue(pageStructure.getShapes().isEmpty());
        assertTrue(pageStructure.getLines().isEmpty());
        assertTrue(pageStructure.getImages().isEmpty());
    }

    @Test
    void testAddTextElement() {
        PDFTextElement textElement = new PDFTextElement(
                "Test Text",
                new Point2D.Float(10.0f, 20.0f),
                12.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        pageStructure.addTextElement(textElement);

        List<PDFTextElement> elements = pageStructure.getElements();
        assertEquals(1, elements.size());
        assertSame(textElement, elements.get(0));
    }

    @Test
    void testAddMultipleTextElements() {
        PDFTextElement element1 = new PDFTextElement(
                "Text 1",
                new Point2D.Float(0.0f, 0.0f),
                10.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        PDFTextElement element2 = new PDFTextElement(
                "Text 2",
                new Point2D.Float(10.0f, 10.0f),
                14.0f,
                new PDType1Font(Standard14Fonts.FontName.TIMES_ROMAN),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        pageStructure.addTextElement(element1);
        pageStructure.addTextElement(element2);

        List<PDFTextElement> elements = pageStructure.getElements();
        assertEquals(2, elements.size());
        assertSame(element1, elements.get(0));
        assertSame(element2, elements.get(1));
    }

    @Test
    void testAddShapeElement() {
        RGBA fillColor = new RGBA(255, 0, 0, 255);
        RGBA strokeColor = new RGBA(0, 255, 0, 255);
        PDFShapeElement shapeElement = new PDFShapeElement(10.0f, 20.0f, 30.0f, 40.0f, fillColor, strokeColor);

        pageStructure.addShapeElement(shapeElement);

        List<PDFShapeElement> shapes = pageStructure.getShapes();
        assertEquals(1, shapes.size());
        assertSame(shapeElement, shapes.get(0));
    }

    @Test
    void testAddMultipleShapeElements() {
        PDFShapeElement shape1 = new PDFShapeElement(0.0f, 0.0f, 10.0f, 10.0f, null, null);
        PDFShapeElement shape2 = new PDFShapeElement(20.0f, 20.0f, 30.0f, 30.0f, null, null);

        pageStructure.addShapeElement(shape1);
        pageStructure.addShapeElement(shape2);

        List<PDFShapeElement> shapes = pageStructure.getShapes();
        assertEquals(2, shapes.size());
        assertSame(shape1, shapes.get(0));
        assertSame(shape2, shapes.get(1));
    }

    @Test
    void testAddLineElement() {
        RGBA strokeColor = new RGBA(0, 0, 255, 255);
        PDFLineElement lineElement = new PDFLineElement(0.0f, 0.0f, 100.0f, 100.0f, strokeColor);

        pageStructure.addLineElement(lineElement);

        List<PDFLineElement> lines = pageStructure.getLines();
        assertEquals(1, lines.size());
        assertSame(lineElement, lines.get(0));
    }

    @Test
    void testAddMultipleLineElements() {
        PDFLineElement line1 = new PDFLineElement(0.0f, 0.0f, 10.0f, 10.0f, null);
        PDFLineElement line2 = new PDFLineElement(20.0f, 20.0f, 30.0f, 30.0f, null);

        pageStructure.addLineElement(line1);
        pageStructure.addLineElement(line2);

        List<PDFLineElement> lines = pageStructure.getLines();
        assertEquals(2, lines.size());
        assertSame(line1, lines.get(0));
        assertSame(line2, lines.get(1));
    }

    @Test
    void testAddImageElement() {
        Image image = new Image();
        image.size = new Size();
        image.size.width = 100.0f;
        image.size.height = 200.0f;
        image.position = new Position();
        image.position.x = 50.0f;
        image.position.y = 75.0f;
        image.bufferedImage = new BufferedImage(100, 200, BufferedImage.TYPE_INT_RGB);

        pageStructure.addImageElement(image);

        List<Image> images = pageStructure.getImages();
        assertEquals(1, images.size());
        assertSame(image, images.get(0));
    }

    @Test
    void testAddMultipleImageElements() {
        Image image1 = new Image();
        image1.size = new Size();
        image1.size.width = 50.0f;
        image1.size.height = 50.0f;

        Image image2 = new Image();
        image2.size = new Size();
        image2.size.width = 100.0f;
        image2.size.height = 100.0f;

        pageStructure.addImageElement(image1);
        pageStructure.addImageElement(image2);

        List<Image> images = pageStructure.getImages();
        assertEquals(2, images.size());
        assertSame(image1, images.get(0));
        assertSame(image2, images.get(1));
    }

    @Test
    void testGettersReturnImmutableLists() {
        // Add some elements
        PDFTextElement textElement = new PDFTextElement(
                "Test",
                new Point2D.Float(0.0f, 0.0f),
                10.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );
        pageStructure.addTextElement(textElement);

        PDFShapeElement shapeElement = new PDFShapeElement(0.0f, 0.0f, 10.0f, 10.0f, null, null);
        pageStructure.addShapeElement(shapeElement);

        PDFLineElement lineElement = new PDFLineElement(0.0f, 0.0f, 10.0f, 10.0f, null);
        pageStructure.addLineElement(lineElement);

        Image image = new Image();
        pageStructure.addImageElement(image);

        // Get the lists
        List<PDFTextElement> elements = pageStructure.getElements();
        List<PDFShapeElement> shapes = pageStructure.getShapes();
        List<PDFLineElement> lines = pageStructure.getLines();
        List<Image> images = pageStructure.getImages();

        // Verify they contain the expected elements
        assertEquals(1, elements.size());
        assertEquals(1, shapes.size());
        assertEquals(1, lines.size());
        assertEquals(1, images.size());

        assertSame(textElement, elements.get(0));
        assertSame(shapeElement, shapes.get(0));
        assertSame(lineElement, lines.get(0));
        assertSame(image, images.get(0));
    }

    @Test
    void testPrettyMethod() {
        // Add various elements
        PDFTextElement textElement = new PDFTextElement(
                "Hello",
                new Point2D.Float(10.0f, 20.0f),
                12.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );
        pageStructure.addTextElement(textElement);

        RGBA fillColor = new RGBA(255, 0, 0, 255);
        PDFShapeElement shapeElement = new PDFShapeElement(5.0f, 10.0f, 15.0f, 20.0f, fillColor, null);
        pageStructure.addShapeElement(shapeElement);

        RGBA strokeColor = new RGBA(0, 255, 0, 255);
        PDFLineElement lineElement = new PDFLineElement(0.0f, 0.0f, 50.0f, 50.0f, strokeColor);
        pageStructure.addLineElement(lineElement);

        Image image = new Image();
        image.size = new Size();
        image.size.width = 100.0f;
        image.size.height = 200.0f;
        pageStructure.addImageElement(image);

        String pretty = pageStructure.pretty();

        assertNotNull(pretty);
        assertTrue(pretty.contains("PDFShapeElement"));
        assertTrue(pretty.contains("PDFLineElement"));
        assertTrue(pretty.contains("PDFTextElement"));
        assertTrue(pretty.contains("Image"));
    }

    @Test
    void testPrettyMethodWithEmptyStructure() {
        String pretty = pageStructure.pretty();

        assertNotNull(pretty);
        assertEquals("", pretty);
    }
}
