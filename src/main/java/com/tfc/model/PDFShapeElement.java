package com.tfc.model;

import org.jetbrains.annotations.NotNull;

import java.awt.geom.Rectangle2D;

public record PDFShapeElement(String id, float x, float y, float width, float height, RGBA fillColor, RGBA strokeColor) {

    public PDFShapeElement(float x, float y, float width, float height, RGBA fillColor, RGBA strokeColor) {
        this(ElementIdGenerator.generateId("SHAPE"), x, y, width, height, fillColor, strokeColor);
    }

    @Override
    public @NotNull String toString() {
        return String.format("PDFShapeElement{id='%s', rect=[x=%.2f, y=%.2f, w=%.2f, h=%.2f], fill=%s, stroke=%s}",
                id, x, y, width, height, fillColor, strokeColor);
    }

    public Rectangle2D.Float getRect() {
        return new Rectangle2D.Float(x, y, width, height);
    }

}