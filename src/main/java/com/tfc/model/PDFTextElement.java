package com.tfc.model;

import org.apache.pdfbox.pdmodel.font.PDFont;

import java.awt.geom.Point2D;

public class PDFTextElement {

    private final String id;
    private final String text;
    private final Point2D position;
    private final float fontSize;
    private final PDFont font;
    private final double[] ctm;
    private final int pageNumber;

    public PDFTextElement(String text, Point2D position, float fontSize, PDFont font, double[] ctm, int pageNumber) {
        this.id = ElementIdGenerator.generateId("TEXT");
        this.text = text;
        this.position = (Point2D) position.clone();
        this.fontSize = fontSize;
        this.font = font;
        this.ctm = ctm;
        this.pageNumber = pageNumber;
        if (this.font == null) {
            throw new NullPointerException("font is null");
        }
    }

    public String getId() {
        return id;
    }

    public String getText() {
        return text;
    }

    public Point2D getPosition() {
        return position;
    }

    public float getFontSize() {
        return fontSize;
    }

    public PDFont getFont() {
        return font;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    @Override
    public String toString() {
        return "PDFTextElement{" +
                "id='" + id + '\'' +
                ", text='" + text + '\'' +
                ", position=" + position +
                ", fontSize=" + fontSize +
                ", font='" + font.getName() + '\'' +
                ", pageNumber=" + pageNumber +
                '}';
    }

    public double[] getCtm() {
        return ctm;
    }
}