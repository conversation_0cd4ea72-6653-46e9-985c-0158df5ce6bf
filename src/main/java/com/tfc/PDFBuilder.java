package com.tfc;

import com.tfc.model.*;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.geom.Rectangle2D;
import java.io.File;
import java.io.IOException;
import java.util.List;

public class PDFBuilder {

    private static final Logger logger = LoggerFactory.getLogger(PDFBuilder.class);

    public static void buildMultiPagePDF(List<PDFPageStructure> pageStructures, File outputFile) throws IOException {
        PDDocument document = new PDDocument();

        for (PDFPageStructure pageStructure : pageStructures) {

            List<PDFTextElement> textElements = pageStructure.getElements();
            List<PDFShapeElement> shapeElements = pageStructure.getShapes();
            List<PDFLineElement> lineElements = pageStructure.getLines();
            List<Image> images = pageStructure.getImages();

            PDPage page = new PDPage(PDRectangle.A4); // Use original size if known
            document.addPage(page);

            PDPageContentStream contentStream = new PDPageContentStream(document, page);

            drawShapes(shapeElements, contentStream);
            drawLines(lineElements, contentStream);
            drawText(textElements, document, contentStream);
            drawImages(images, document, contentStream);

            contentStream.close();
        }

        document.save(outputFile);
        document.close();
    }

    private static void drawImages(List<Image> images, PDDocument document, PDPageContentStream contentStream) throws IOException {
        for (Image image : images) {
            if (image.bufferedImage != null) {
                PDImageXObject pdImage = LosslessFactory.createFromImage(document, image.bufferedImage);
                contentStream.drawImage(pdImage, image.position.x, image.position.y, image.size.width, image.size.height);
                logger.debug("Draw image [{}]: {} at {},{} with size {}x{}", image.getId(), image.bufferedImage, image.position.x, image.position.y, image.size.width, image.size.height);
            }
        }
    }

    private static void drawText(List<PDFTextElement> textElements, PDDocument document, PDPageContentStream contentStream) throws IOException {
        for (PDFTextElement text : textElements) {
            PDFont font = PDType0Font.load(document, new File("examples/Arial Nova Cond.ttf"));
            float x = (float) text.getPosition().getX();
            float y = (float) text.getPosition().getY();

            // Approximate the correct font size
            float fontSize = (float) (text.getFontSize() * text.getCtm()[0]); // use scaleX
            // Optional: refine font size calculation if text looks stretched vertically

            // Compute ascent from the loaded font
            float ascent = font.getFontDescriptor().getAscent() / 1000f * fontSize;
            float adjustedY = y + ascent; // Align by top of text

            contentStream.beginText();
            contentStream.setFont(font, fontSize);
            contentStream.newLineAtOffset(x, adjustedY);
            contentStream.showText(text.getText());
            contentStream.endText();

            logger.debug("Draw text [{}]: '{}' at ({}, {}) with custom font '{}' and size {}",
                    text.getId(), text.getText(), x, adjustedY, font.getName(), fontSize);
        }
    }

    private static void drawLines(List<PDFLineElement> lineElements, PDPageContentStream contentStream) throws IOException {
        for (PDFLineElement line : lineElements) {
            if (line.strokeColor() != null) {
                RGBA stroke = line.strokeColor();
                if (!stroke.isWhite()) {
                    contentStream.setStrokingColor(stroke.toColor());
                    contentStream.moveTo(line.x1(), line.y1());
                    contentStream.lineTo(line.x2(), line.y2());
                    contentStream.stroke();
                    logger.debug("Draw line [{}]: from ({}, {}) to ({}, {}) with stroke {}",
                            line.id(), line.x1(), line.y1(), line.x2(), line.y2(), stroke);
                }
            }
        }
    }

    private static void drawShapes(List<PDFShapeElement> shapeElements, PDPageContentStream contentStream) throws IOException {
        int shapeCount = 0;
        int maxShapes = Integer.MAX_VALUE; //for debugging
        for (PDFShapeElement shape : shapeElements) {
            Rectangle2D.Float rect = shape.getRect();

            if (shape.fillColor() != null) {
                RGBA fill = shape.fillColor();
                //noinspection ConstantValue
                if (!fill.isWhite() || maxShapes == Integer.MAX_VALUE) {
                    contentStream.setNonStrokingColor(fill.toColor());
                    contentStream.addRect(rect.x, rect.y, rect.width, rect.height);
                    contentStream.fill();
                    logger.debug("Draw shape [{}]: {} with fill {}", shape.id(), rect, fill);
                    shapeCount++;
                    if (shapeCount >= maxShapes) {
                        return;
                    }
                }
            }

            if (shape.strokeColor() != null) {
                RGBA stroke = shape.strokeColor();
                //noinspection ConstantValue
                if (!stroke.isWhite() || maxShapes == Integer.MAX_VALUE) {
                    contentStream.setStrokingColor(stroke.toColor());
                    contentStream.addRect(rect.x, rect.y, rect.width, rect.height);
                    contentStream.stroke();
                    logger.debug("Draw shape [{}]: {} with stroke {}", shape.id(), rect, stroke);
                    shapeCount++;
                    if (shapeCount >= maxShapes) {
                        return;
                    }
                }
            }
        }
    }

}
