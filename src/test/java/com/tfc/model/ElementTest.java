package com.tfc.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ElementTest {

    @Test
    void testToStringWithNullPosition() {
        Element element = new Element();
        
        String expected = "Element{position=null}";
        assertEquals(expected, element.toString());
    }

    @Test
    void testToStringWithPosition() {
        Element element = new Element();
        Position position = new Position();
        position.x = 10.0f;
        position.y = 20.0f;
        element.position = position;
        
        String expected = "Element{position=Position{x=10.0, y=20.0}}";
        assertEquals(expected, element.toString());
    }

    @Test
    void testPositionFieldIsPublic() {
        Element element = new Element();
        Position position = new Position();
        position.x = 50.0f;
        position.y = 75.0f;
        
        // Test that position field can be accessed and modified directly
        element.position = position;
        
        assertSame(position, element.position);
        assertEquals(50.0f, element.position.x);
        assertEquals(75.0f, element.position.y);
    }

    @Test
    void testElementCanBeExtended() {
        // Test that Element can be used as a base class
        TestElement testElement = new TestElement();
        Position position = new Position();
        position.x = 100.0f;
        position.y = 200.0f;
        testElement.position = position;
        
        assertEquals(100.0f, testElement.position.x);
        assertEquals(200.0f, testElement.position.y);
    }

    // Helper class to test inheritance
    private static class TestElement extends Element {
        // Empty implementation for testing inheritance
    }
}
