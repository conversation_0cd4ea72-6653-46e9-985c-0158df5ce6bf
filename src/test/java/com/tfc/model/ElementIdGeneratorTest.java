package com.tfc.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ElementIdGeneratorTest {

    @BeforeEach
    void setUp() {
        // Reset counter before each test to ensure predictable results
        ElementIdGenerator.resetCounter();
    }

    @Test
    void testGenerateIdWithPrefix() {
        String id1 = ElementIdGenerator.generateId("TEXT");
        String id2 = ElementIdGenerator.generateId("SHAPE");
        String id3 = ElementIdGenerator.generateId("LINE");

        assertEquals("TEXT_000001", id1);
        assertEquals("SHAPE_000002", id2);
        assertEquals("LINE_000003", id3);
    }

    @Test
    void testGenerateIdIncrementsCounter() {
        String id1 = ElementIdGenerator.generateId("TEST");
        String id2 = ElementIdGenerator.generateId("TEST");
        String id3 = ElementIdGenerator.generateId("TEST");

        assertEquals("TEST_000001", id1);
        assertEquals("TEST_000002", id2);
        assertEquals("TEST_000003", id3);
    }

    @Test
    void testGenerateIdWithEmptyPrefix() {
        String id = ElementIdGenerator.generateId("");
        assertEquals("_000001", id);
    }

    @Test
    void testGenerateIdWithNullPrefix() {
        String id = ElementIdGenerator.generateId(null);
        assertEquals("null_000001", id);
    }

    @Test
    void testResetCounter() {
        // Generate some IDs
        ElementIdGenerator.generateId("TEST");
        ElementIdGenerator.generateId("TEST");
        ElementIdGenerator.generateId("TEST");

        // Reset counter
        ElementIdGenerator.resetCounter();

        // Next ID should start from 1 again
        String id = ElementIdGenerator.generateId("TEST");
        assertEquals("TEST_000001", id);
    }

    @Test
    void testGetCurrentCounter() {
        assertEquals(1, ElementIdGenerator.getCurrentCounter());

        ElementIdGenerator.generateId("TEST");
        assertEquals(2, ElementIdGenerator.getCurrentCounter());

        ElementIdGenerator.generateId("TEST");
        assertEquals(3, ElementIdGenerator.getCurrentCounter());
    }

    @Test
    void testIdUniquenessAcrossThreads() throws InterruptedException {
        final int numThreads = 10;
        final int idsPerThread = 100;
        final String[] ids = new String[numThreads * idsPerThread];

        Thread[] threads = new Thread[numThreads];

        for (int i = 0; i < numThreads; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                for (int j = 0; j < idsPerThread; j++) {
                    ids[threadIndex * idsPerThread + j] = ElementIdGenerator.generateId("THREAD");
                }
            });
        }

        // Start all threads
        for (Thread thread : threads) {
            thread.start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }

        // Check that all IDs are unique
        for (int i = 0; i < ids.length; i++) {
            assertNotNull(ids[i], "ID at index " + i + " should not be null");
            for (int j = i + 1; j < ids.length; j++) {
                assertNotEquals(ids[i], ids[j], "IDs should be unique: " + ids[i] + " vs " + ids[j]);
            }
        }
    }

    @Test
    void testIdFormat() {
        String id = ElementIdGenerator.generateId("ELEMENT");
        
        // Check format: PREFIX_NNNNNN
        assertTrue(id.matches("ELEMENT_\\d{6}"), "ID should match format PREFIX_NNNNNN");
        assertTrue(id.startsWith("ELEMENT_"), "ID should start with prefix");
        
        String numberPart = id.substring(id.lastIndexOf('_') + 1);
        assertEquals(6, numberPart.length(), "Number part should be 6 digits");
    }

    @Test
    void testLargeCounterValues() {
        // Reset and set a large starting value
        ElementIdGenerator.resetCounter();
        
        // Generate many IDs to test large counter values
        for (int i = 0; i < 999999; i++) {
            ElementIdGenerator.generateId("TEST");
        }
        
        String id = ElementIdGenerator.generateId("TEST");
        assertEquals("TEST_1000000", id);
    }
}
