package com.tfc.state;

import com.tfc.model.PDFLineElement;
import com.tfc.model.PDFShapeElement;
import com.tfc.model.RGBA;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.awt.geom.Point2D;
import java.awt.geom.Rectangle2D;

/**
 * Manages shape operations in PDF processing.
 * Responsible for creating and manipulating shape elements.
 */
public class ShapeManager {
    private static final Logger logger = LoggerFactory.getLogger(ShapeManager.class);

    private final GraphicsStateManager graphicsStateManager;
    private final ClippingPathManager clippingPathManager;
    private final ColorManager colorManager;
    private final DocumentBuilder documentBuilder;

    public ShapeManager(GraphicsStateManager graphicsStateManager, 
                        ClippingPathManager clippingPathManager,
                        ColorManager colorManager,
                        DocumentBuilder documentBuilder) {
        this.graphicsStateManager = graphicsStateManager;
        this.clippingPathManager = clippingPathManager;
        this.colorManager = colorManager;
        this.documentBuilder = documentBuilder;
    }

    /**
     * Consumes the current rectangle and creates a filled shape element.
     */
    public void consumeCurrentRectangleFill() {
        Rectangle2D.Float currentRectangle = clippingPathManager.getCurrentRectangle();
        if (currentRectangle != null) {
            // Check if the rectangle intersects with the clipping path
            if (!clippingPathManager.hasClippingPath() ||
                clippingPathManager.getClippingPathRect() == null ||
                clippingPathManager.intersectsClippingPath(currentRectangle)) {

                // Transform both the origin and the opposite corner
                double[] origin = graphicsStateManager.transformPoint(
                    currentRectangle.getX(),
                    currentRectangle.getY()
                );

                double[] corner = graphicsStateManager.transformPoint(
                    currentRectangle.getX() + currentRectangle.getWidth(),
                    currentRectangle.getY() + currentRectangle.getHeight()
                );

                // Calculate the transformed width and height
                float transformedWidth = (float) Math.abs(corner[0] - origin[0]);
                float transformedHeight = (float) Math.abs(corner[1] - origin[1]);

                // Use the minimum coordinates as the new origin to handle rotations correctly
                float newX = (float) Math.min(origin[0], corner[0]);
                float newY = (float) Math.min(origin[1], corner[1]);

                PDFShapeElement rectElement = new PDFShapeElement(
                        newX,
                        newY,
                        transformedWidth,
                        transformedHeight,
                        colorManager.getNonStrokingColor(),
                        null
                );

                documentBuilder.addShapeElement(rectElement);
            }
            clippingPathManager.setCurrentRectangle(null);
        }
    }

    /**
     * Consumes the current rectangle and creates a stroked shape element.
     */
    public void consumeCurrentRectangleStroke() {
        Rectangle2D.Float currentRectangle = clippingPathManager.getCurrentRectangle();
        if (currentRectangle != null) {
            // Check if the rectangle intersects with the clipping path
            if (!clippingPathManager.hasClippingPath() || 
                clippingPathManager.getClippingPathRect() == null || 
                clippingPathManager.intersectsClippingPath(currentRectangle)) {

                // Transform both the origin and the opposite corner
                double[] origin = graphicsStateManager.transformPoint(
                    currentRectangle.getX(), 
                    currentRectangle.getY()
                );

                double[] corner = graphicsStateManager.transformPoint(
                    currentRectangle.getX() + currentRectangle.getWidth(),
                    currentRectangle.getY() + currentRectangle.getHeight()
                );

                // Calculate the transformed width and height
                float transformedWidth = (float) Math.abs(corner[0] - origin[0]);
                float transformedHeight = (float) Math.abs(corner[1] - origin[1]);

                // Use the minimum coordinates as the new origin to handle rotations correctly
                float newX = (float) Math.min(origin[0], corner[0]);
                float newY = (float) Math.min(origin[1], corner[1]);

                PDFShapeElement rectElement = new PDFShapeElement(
                        newX,
                        newY,
                        transformedWidth,
                        transformedHeight,
                        null,
                        colorManager.getStrokingColor()
                );

                documentBuilder.addShapeElement(rectElement);
            }
            clippingPathManager.setCurrentRectangle(null);
        }
    }

    /**
     * Consumes the current rectangle and creates a filled and stroked shape element.
     */
    public void consumeCurrentRectangleFillAndStroke() {
        Rectangle2D.Float currentRectangle = clippingPathManager.getCurrentRectangle();
        if (currentRectangle != null) {
            // Check if the rectangle intersects with the clipping path
            if (!clippingPathManager.hasClippingPath() || 
                clippingPathManager.getClippingPathRect() == null || 
                clippingPathManager.intersectsClippingPath(currentRectangle)) {

                // Transform both the origin and the opposite corner
                double[] origin = graphicsStateManager.transformPoint(
                    currentRectangle.getX(), 
                    currentRectangle.getY()
                );

                double[] corner = graphicsStateManager.transformPoint(
                    currentRectangle.getX() + currentRectangle.getWidth(),
                    currentRectangle.getY() + currentRectangle.getHeight()
                );

                // Calculate the transformed width and height
                float transformedWidth = (float) Math.abs(corner[0] - origin[0]);
                float transformedHeight = (float) Math.abs(corner[1] - origin[1]);

                // Use the minimum coordinates as the new origin to handle rotations correctly
                float newX = (float) Math.min(origin[0], corner[0]);
                float newY = (float) Math.min(origin[1], corner[1]);

                PDFShapeElement rectElement = new PDFShapeElement(
                        newX,
                        newY,
                        transformedWidth,
                        transformedHeight,
                        colorManager.getNonStrokingColor(),
                        colorManager.getStrokingColor()
                );

                documentBuilder.addShapeElement(rectElement);
            }
            clippingPathManager.setCurrentRectangle(null);
        }
    }

    /**
     * Consumes the current rectangle without creating any shape element.
     * This is used for the 'n' operator in PDF, which ends the path without filling or stroking.
     */
    public void consumeCurrentRectangleWithoutFillOrStroke() {
        Rectangle2D.Float currentRectangle = clippingPathManager.getCurrentRectangle();
        if (currentRectangle != null) {
            // Simply clear the current rectangle without creating any shape element
            clippingPathManager.setCurrentRectangle(null);
        }
    }

    /**
     * Sets the current point for path operations.
     * This is used for the 'm' operator in PDF, which moves to a point without drawing.
     * 
     * @param x The x-coordinate of the point.
     * @param y The y-coordinate of the point.
     */
    public void setCurrentPoint(float x, float y) {
        clippingPathManager.setCurrentPoint(x, y);
    }

    /**
     * Creates a line segment from the current point to the specified point.
     * This is used for the 'l' operator in PDF, which draws a line from the current point.
     * 
     * @param x The x-coordinate of the end point.
     * @param y The y-coordinate of the end point.
     */
    public void createLineSegment(float x, float y) {
        Point2D.Float currentPoint = clippingPathManager.getCurrentPoint();
        if (currentPoint != null) {
            // Transform the points using the current CTM
            double[] start = graphicsStateManager.transformPoint(currentPoint.x, currentPoint.y);
            double[] end = graphicsStateManager.transformPoint(x, y);

            // Create a line element
            PDFLineElement lineElement = new PDFLineElement(
                (float) start[0],
                (float) start[1],
                (float) end[0],
                (float) end[1],
                colorManager.getStrokingColor()
            );

            // Add the line element to the document
            documentBuilder.addLineElement(lineElement);

            // Update the current point
            clippingPathManager.setCurrentPoint(x, y);
        }
    }
}
